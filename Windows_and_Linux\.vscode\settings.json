{
    // Python interpreter configuration
    "python.defaultInterpreterPath": "./myvenv/Scripts/python.exe",
    "python.terminal.activateEnvironment": true,
    
    // Linting and formatting
    "python.linting.enabled": true,
    "python.linting.ruffEnabled": true,
    "python.linting.mypyEnabled": true,
    "python.linting.pylintEnabled": false,
    "python.linting.flake8Enabled": false,
    
    // Formatting
    "python.formatting.provider": "black",
    "python.formatting.blackArgs": ["--line-length", "120"],
    
    // Import sorting
    "python.sortImports.args": ["--profile", "black"],
    
    // Auto-format on save
    "editor.formatOnSave": true,
    "editor.codeActionsOnSave": {
        "source.organizeImports": true
    },
    
    // File associations
    "files.associations": {
        "*.py": "python"
    },
    
    // Editor settings
    "editor.rulers": [120],
    "editor.tabSize": 4,
    "editor.insertSpaces": true,
    
    // Python-specific settings
    "python.analysis.typeCheckingMode": "basic",
    "python.analysis.autoImportCompletions": true,
    
    // Exclude patterns for search and file watching
    "files.exclude": {
        "**/__pycache__": true,
        "**/*.pyc": true,
        "**/myvenv": true,
        "**/dist": true,
        "**/build": true,
        "**/.mypy_cache": true,
        "**/.ruff_cache": true
    },
    
    // Search exclude patterns
    "search.exclude": {
        "**/myvenv": true,
        "**/dist": true,
        "**/build": true,
        "**/__pycache__": true,
        "**/.mypy_cache": true,
        "**/.ruff_cache": true
    }
}
